import { INodeType, INodeTypeDescription, ILoadOptionsFunctions, INodePropertyOptions, IHookFunctions, IExecuteFunctions, IHttpRequestMethods, IDataObject, INodeExecutionData } from 'n8n-workflow';
export declare class Kylas implements INodeType {
    description: INodeTypeDescription;
    methods: {
        loadOptions: {
            getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
        };
    };
    execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]>;
}
export declare function getCachedSystemFields(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<IDataObject[]>;
export declare function kylasApiRequest(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions, method: IHttpRequestMethods, endpoint: string, body: IDataObject): Promise<IDataObject>;
