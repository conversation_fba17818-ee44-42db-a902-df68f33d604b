import type { INodeProperties, IHttpRequestOptions } from 'n8n-workflow';

const showOnlyForLeadCreate = {
    operation: ['create'],
    resource: ['lead'],
};



export const customFieldsDescription: INodeProperties[] = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom fields for the lead",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Property",
                name: "property",
                values: [
                    {
                        displayName: "Field Name or ID",
                        name: "name",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: 'getLeadCustomFields',
                        },
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    },
                    {
                        displayName: "Field Value",
                        name: "value",
                        type: "string",
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    }
                ]
            },
        ],
        routing: {
            send: {
                preSend: [
                    async function (this: {
                        getNodeParameter: (parameterName: string, defaultValue?: unknown) => unknown;
                    }, requestOptions: IHttpRequestOptions) {
                        const customFields = this.getNodeParameter('customFields.property', []) as Array<{
                            name: string;
                            value: string;
                        }>;

                        // Add each custom field to the request body
                        customFields.forEach(field => {
                            if (field.name && field.value) {
                                // Only add if value is not empty/undefined
                                if (field.value !== undefined && field.value !== '' && field.value !== null) {
                                    if (!requestOptions.body) {
                                        requestOptions.body = {};
                                    }
                                    if (typeof requestOptions.body === 'object' && requestOptions.body !== null && !Array.isArray(requestOptions.body)) {
                                        (requestOptions.body as Record<string, unknown>)[field.name] = field.value;
                                    }
                                }
                            }
                        });

                        return requestOptions;
                    }
                ]
            },
        },
    },
];
