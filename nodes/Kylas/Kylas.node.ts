import { userDescription } from './resources/user';
import { companyDescription } from './resources/company';
import { leadDescription } from './resources/lead';
import {
	INodeType,
	INodeTypeDescription,
	ILoadOptionsFunctions,
	INodePropertyOptions,
	IHookFunctions,
	IExecuteFunctions,
	IHttpRequestMethods,
	IDataObject,
	IHttpRequestOptions,
	NodeApiError,
	JsonObject,
	INodeExecutionData
} from 'n8n-workflow';

export class <PERSON>ylas implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Kyla<PERSON>',
		name: 'kyla<PERSON>',
		icon: { light: 'file:kylas.svg', dark: 'file:kylas.dark.svg' },
		group: ['transform'],
		version: 1,
		subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
		description: 'Interact with the Kylas API',
		defaults: {
			name: '<PERSON>yla<PERSON>',
		},
		usableAsTool: true,
		inputs: ['main'],
		outputs: ['main'],
		credentials: [{ name: 'kylas<PERSON><PERSON>', required: true }],
		requestDefaults: {
			baseURL: 'https://api-qa.sling-dev.com',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json',
			},
		},
		properties: [
			{
				displayName: 'Resource',
				name: 'resource',
				type: 'options',
				noDataExpression: true,
				options: [
					{
						name: 'User',
						value: 'user',
					},
					{
						name: 'Company',
						value: 'company',
					},
					{
						name: 'Lead',
						value: 'lead',
					},
				],
				default: 'user',
			},
			...userDescription,
			...companyDescription,
			...leadDescription
		],
	};

	methods = {
		loadOptions: {
			async getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                console.log("=== getLeadCustomFields CALLED ===");
                const returnData: INodePropertyOptions[] = [];
                const fields = await getCachedSystemFields.call(this);
                (fields as IDataObject[])
                    .filter(field => field.active
                        && field.standard === false
                        && field.type !== 'LOOK_UP'
                        && field.type !== 'MULTI_PICKLIST'
                        && field.type !== 'PICK_LIST'
                    )
                    .forEach(field => {
                        // Add field type information to the display name for PICK_LIST fields
                        returnData.push({
                            name: field.displayName as string,
                            value: field.name as string,
                        });
                    });
                console.log(`getLeadCustomFields returning ${returnData.length} fields`);
                return returnData;
            },
		},
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		// This node uses declarative routing, so the execute method
		// should not be called directly. Return empty result.
		return [[]];
	}
}
let systemFieldsCache: IDataObject[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export async function getCachedSystemFields(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<any[]> {
    const now = Date.now();

    // Check if cache is valid (not expired)
    if (systemFieldsCache && (now - cacheTimestamp) < CACHE_DURATION) {
        console.log('Using cached system fields');
        return systemFieldsCache;
    }

    // Cache is expired or doesn't exist, fetch fresh data
    console.log('Fetching fresh system fields from API');
    const customFields = await kylasApiRequest.call(this, 'GET', '/v1/layouts/leads/system-fields?view=create', {});
    const fields = JSON.parse(customFields.data);

    // Update cache
    systemFieldsCache = fields;
    cacheTimestamp = now;

    return fields;
}


export async function kylasApiRequest(
    this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions,
    method: IHttpRequestMethods,
    endpoint: string,
    body: IDataObject
): Promise<any> {
    // const authenticationMethod = this.getNodeParameter('authentication', 0);
    console.log("URI ->" + `https://api-qa.sling-dev.com${endpoint}`);
    const options: IHttpRequestOptions = {
        headers: {
            Accept: 'application/json',
        },
        method,
        url: `https://api-qa.sling-dev.com${endpoint}`,
    };

    // console.log("options->" + JSON.stringify(options));

    if (Object.keys(body).length !== 0) {
        options.body = body;
        options.json = true;
    }


    try {
        const credentialType = 'kylasApi';
        // console.log("option->" + JSON.stringify(options.body));
        const responseData = await this.helpers.requestWithAuthentication.call(
            this,
            credentialType,
            options,
        );


        // console.log("responseData 2->" + JSON.stringify(responseData));
        if (responseData.success === false) {
            throw new NodeApiError(this.getNode(), responseData as JsonObject);
        }
        console.log("Return success")
        return {
            data: responseData
        };
    } catch (error) {
        throw new NodeApiError(this.getNode(), error as JsonObject);
    }
}

